import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { viteStaticCopy } from 'vite-plugin-static-copy'

export default defineConfig({
  plugins: [
    vue(),
    viteStaticCopy({
      targets: [
        {
          src: 'public/manifest.json',
          dest: ''
        },
        {
          src: 'public/icons',
          dest: ''
        }
      ],
    }),
    // 自定义插件来处理HTML中的CSS和JS路径
    {
      name: 'fix-paths',
      generateBundle(options, bundle) {
        // 找到HTML文件
        const htmlFile = Object.keys(bundle).find(fileName => fileName.endsWith('.html'))
        if (htmlFile && bundle[htmlFile].type === 'asset') {
          let html = bundle[htmlFile].source

          // 修复所有绝对路径为相对路径
          html = html.replace(/src="\/([^"]+)"/g, 'src="./$1"')
          html = html.replace(/href="\/([^"]+)"/g, 'href="./$1"')

          bundle[htmlFile].source = html
        }
      }
    }
  ],
  build: {
    rollupOptions: {
      input: 'index.html',
      output: {
        entryFileNames: 'main.js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    outDir: 'dist',
    emptyOutDir: true
  },
  base: './'
})
