// Chrome存储API封装
export const storage = {
  // 保存历史记录
  async saveHistory(record) {
    try {
      // 检查Chrome存储API是否可用
      if (!chrome || !chrome.storage || !chrome.storage.local) {
        console.warn('Chrome存储API不可用，跳过历史记录保存')
        return null
      }

      const result = await chrome.storage.local.get(['history'])
      const history = result.history || []

      // 验证记录数据
      if (!record || !record.input || !record.output) {
        console.warn('无效的历史记录数据，跳过保存')
        return null
      }

      // 添加时间戳
      const newRecord = {
        ...record,
        timestamp: Date.now(),
        id: Date.now().toString()
      }

      // 添加到历史记录开头，限制最多100条
      history.unshift(newRecord)
      if (history.length > 100) {
        history.splice(100)
      }

      await chrome.storage.local.set({ history })
      return newRecord
    } catch (error) {
      console.error('保存历史记录失败:', error)
      // 不抛出错误，避免影响主要功能
      return null
    }
  },

  // 获取历史记录
  async getHistory() {
    try {
      // 检查Chrome存储API是否可用
      if (!chrome || !chrome.storage || !chrome.storage.local) {
        console.warn('Chrome存储API不可用，返回空历史记录')
        return []
      }

      const result = await chrome.storage.local.get(['history'])
      return Array.isArray(result.history) ? result.history : []
    } catch (error) {
      console.error('获取历史记录失败:', error)
      return []
    }
  },

  // 清空历史记录
  async clearHistory() {
    try {
      await chrome.storage.local.set({ history: [] })
    } catch (error) {
      console.error('清空历史记录失败:', error)
      throw error
    }
  },

  // 删除单条历史记录
  async deleteHistoryItem(id) {
    try {
      const result = await chrome.storage.local.get(['history'])
      const history = result.history || []
      const filteredHistory = history.filter(item => item.id !== id)
      await chrome.storage.local.set({ history: filteredHistory })
    } catch (error) {
      console.error('删除历史记录失败:', error)
      throw error
    }
  }
}
