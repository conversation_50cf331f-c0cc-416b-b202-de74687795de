<template>
  <div class="container">
    <!-- 左侧输入区域 -->
    <div class="input-section">
      <div class="section-title">
        {{ isEncryptMode ? '明文' : '密文' }}
        <div class="button-group" style="float: right;">
          <button class="btn btn-secondary btn-small" @click="pasteText">粘贴</button>
          <button class="btn btn-secondary btn-small" @click="clearInput">清空</button>
        </div>
      </div>
      <textarea 
        v-model="inputText" 
        class="textarea" 
        :placeholder="isEncryptMode ? '请输入要加密的明文...' : '请输入要解密的密文...'"
      ></textarea>
    </div>

    <!-- 中间控制区域 -->
    <div class="control-section">
      <!-- 模式切换 -->
      <div class="control-group">
        <div class="control-label">模式</div>
        <div class="toggle-btn">
          <button 
            :class="{ active: isManualMode }" 
            @click="isManualMode = true"
          >
            手动
          </button>
          <button 
            :class="{ active: !isManualMode }" 
            @click="isManualMode = false"
          >
            自动
          </button>
        </div>
      </div>

      <!-- 操作类型 -->
      <div class="control-group">
        <div class="control-label">操作</div>
        <div class="toggle-btn">
          <button 
            :class="{ active: isEncryptMode }" 
            @click="isEncryptMode = true"
          >
            加密
          </button>
          <button 
            :class="{ active: !isEncryptMode }" 
            @click="isEncryptMode = false"
          >
            解密
          </button>
        </div>
      </div>

      <!-- 算法选择 -->
      <div class="control-group">
        <div class="control-label">算法</div>
        <div class="custom-select" @click="toggleDropdown" ref="selectRef">
          <div class="select-display">
            <span>{{ getSelectedAlgorithmLabel() }}</span>
            <span class="select-arrow" :class="{ 'open': isDropdownOpen }">▼</span>
          </div>
          <ul v-show="isDropdownOpen" class="select-dropdown">
            <li
              v-for="algo in algorithms"
              :key="algo.value"
              :class="{ 'selected': algo.value === selectedAlgorithm }"
              :title="algo.title"
              @click.stop="selectAlgorithm(algo.value)"
            >
              {{ algo.label }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 执行按钮 -->
      <div class="control-group">
        <button class="btn btn-primary" @click="processText" style="width: 100%;">
          {{ isEncryptMode ? '加密 >>>' : '解密 >>>' }}
        </button>
      </div>

      <!-- 历史记录按钮 -->
      <div class="control-group">
        <button class="btn btn-secondary" @click="$emit('show-history')" style="width: 100%;">
          历史记录
        </button>
      </div>
      
      <!-- 执行按钮 -->
      <div class="control-group">
        <button class="btn btn-secondary" @click="openByTab" style="width: 100%;">
          网页打开
        </button>
      </div>
    </div>

    <!-- 右侧输出区域 -->
    <div class="output-section">
      <div class="section-title">
        {{ isEncryptMode ? '密文' : '明文' }}
        <div class="button-group" style="float: right;">
          <button class="btn btn-secondary btn-small" @click="copyResult">复制</button>
          <button class="btn btn-secondary btn-small" @click="clearOutput">清空</button>
          <button 
            v-if="showJsonToggle" 
            class="btn btn-secondary btn-small" 
            @click="toggleJsonFormat"
          >
            {{ isJsonFormatted ? '原始' : '格式化' }}
          </button>
        </div>
      </div>
      <textarea 
        v-model="outputText" 
        class="textarea" 
        :class="{ 'json-formatted': isJsonFormatted }"
        readonly
        placeholder="处理结果将显示在这里..."
      ></textarea>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { algorithms, encryptFunctions, decryptFunctions, removeEndCode, isValidJSON, formatJSON } from '../utils/crypto.js'
import { storage } from '../utils/storage.js'

export default {
  name: 'MainView',
  props: {
    historyData: {
      type: Object,
      default: null
    }
  },
  emits: ['show-history'],
  setup(props) {
    const inputText = ref('')
    const outputText = ref('')
    const isManualMode = ref(false)
    const isEncryptMode = ref(false)
    const selectedAlgorithm = ref('aes')
    const isJsonFormatted = ref(false)
    const rawOutputText = ref('')
    const isDropdownOpen = ref(false)
    const selectRef = ref(null)

    // 监听历史数据变化
    watch(() => props.historyData, (newData) => {
      if (newData) {
        inputText.value = newData.input
        selectedAlgorithm.value = newData.algorithm
        isEncryptMode.value = newData.operation === 'encrypt'

        // 如果是解密操作，也设置输出
        if (newData.operation === 'decrypt') {
          rawOutputText.value = newData.output
          outputText.value = newData.output
        } else {
          rawOutputText.value = ''
          outputText.value = ''
        }

        isJsonFormatted.value = false
      }
    }, { immediate: true })

    // 是否显示JSON格式化按钮
    const showJsonToggle = computed(() => {
      return !isEncryptMode.value && isValidJSON(rawOutputText.value)
    })

    // 自动模式处理
    watch([inputText, isEncryptMode, selectedAlgorithm], () => {
      if (!isManualMode.value && inputText.value.trim()) {
        processText()
      }
    })

    // 处理文本
    const processText = async () => {
      if (!inputText.value.trim()) {
        showToast('请输入要处理的文本')
        return
      }

      try {
        let result
        let processedInput = inputText.value.trim()

        // 验证算法是否存在
        if (!selectedAlgorithm.value) {
          throw new Error('请选择加密算法')
        }

        if (isEncryptMode.value) {
          // 加密
          const encryptFunc = encryptFunctions[selectedAlgorithm.value]
          if (!encryptFunc || typeof encryptFunc !== 'function') {
            throw new Error(`不支持的加密算法: ${selectedAlgorithm.value}`)
          }
          result = encryptFunc(processedInput)
        } else {
          // 解密时处理ENDCODE
          processedInput = removeEndCode(processedInput)

          // 验证输入是否为空
          if (!processedInput.trim()) {
            throw new Error('处理后的输入为空，请检查输入内容')
          }

          const decryptFunc = decryptFunctions[selectedAlgorithm.value]
          if (!decryptFunc || typeof decryptFunc !== 'function') {
            throw new Error(`不支持的解密算法: ${selectedAlgorithm.value}`)
          }
          result = decryptFunc(processedInput)
        }

        // 验证结果
        if (result === null || result === undefined) {
          throw new Error('处理结果为空，请检查输入内容和算法选择')
        }

        rawOutputText.value = result

        // 如果是解密模式且结果是有效JSON，自动格式化
        if (!isEncryptMode.value && isValidJSON(result)) {
          outputText.value = formatJSON(result)
          isJsonFormatted.value = true
        } else {
          outputText.value = result
          isJsonFormatted.value = false
        }

        // 保存到历史记录
        try {
          await storage.saveHistory({
            input: inputText.value,
            output: result,
            algorithm: selectedAlgorithm.value,
            operation: isEncryptMode.value ? 'encrypt' : 'decrypt'
          })
        } catch (storageError) {
          console.warn('保存历史记录失败:', storageError)
          // 不影响主要功能，只是警告
        }

        showToast('处理完成')

      } catch (error) {
        console.error('处理文本错误:', error)

        // 根据错误类型提供更友好的提示
        let errorMessage = error.message || '未知错误'

        if (errorMessage.includes('Cannot read properties of undefined')) {
          errorMessage = 'CryptoJS库加载失败，请刷新页面重试'
        } else if (errorMessage.includes('Malformed UTF-8')) {
          errorMessage = '输入内容格式错误，请检查编码格式'
        } else if (errorMessage.includes('Invalid key length')) {
          errorMessage = '密钥长度错误，请检查输入内容'
        } else if (errorMessage.includes('Invalid base64')) {
          errorMessage = 'Base64格式错误，请检查输入内容'
        } else if (!isEncryptMode.value && errorMessage.includes('padding')) {
          errorMessage = '解密失败，可能是密文格式错误或算法选择错误'
        }

        showToast(`${isEncryptMode.value ? '加密' : '解密'}失败：${errorMessage}`)
      }
    }

    // 切换JSON格式化
    const toggleJsonFormat = () => {
      if (isJsonFormatted.value) {
        outputText.value = rawOutputText.value
        isJsonFormatted.value = false
      } else {
        outputText.value = formatJSON(rawOutputText.value)
        isJsonFormatted.value = true
      }
    }

    // 粘贴文本
    const pasteText = async () => {
      try {
        // 检查剪贴板API是否可用
        if (!navigator.clipboard || !navigator.clipboard.readText) {
          throw new Error('浏览器不支持剪贴板API')
        }

        const text = await navigator.clipboard.readText()
        if (!text) {
          showToast('剪贴板为空')
          return
        }

        inputText.value = text
        showToast('已粘贴文本')
      } catch (error) {
        console.error('粘贴失败:', error)

        let errorMessage = '粘贴失败'
        if (error.name === 'NotAllowedError') {
          errorMessage = '请允许剪贴板权限'
        } else if (error.message.includes('不支持')) {
          errorMessage = '浏览器不支持剪贴板功能'
        }

        showToast(errorMessage)
      }
    }

    // 复制结果
    const copyResult = async () => {
      try {
        if (!outputText.value) {
          showToast('没有可复制的内容')
          return
        }

        // 检查剪贴板API是否可用
        if (!navigator.clipboard || !navigator.clipboard.writeText) {
          // 降级方案：使用传统方法
          const textArea = document.createElement('textarea')
          textArea.value = outputText.value
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          showToast('已复制到剪贴板')
          return
        }

        await navigator.clipboard.writeText(outputText.value)
        showToast('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)

        let errorMessage = '复制失败'
        if (error.name === 'NotAllowedError') {
          errorMessage = '请允许剪贴板权限'
        }

        showToast(errorMessage + '，请手动选择复制')
      }
    }

    // 简单的提示功能
    const showToast = (message) => {
      // 创建临时提示元素
      const toast = document.createElement('div')
      toast.textContent = message
      toast.style.cssText = `
        position: fixed;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        max-width: 300px;
        text-align: center;
      `
      document.body.appendChild(toast)

      // 显示动画
      setTimeout(() => {
        toast.style.opacity = '1'
      }, 10)

      // 1.5秒后移除
      setTimeout(() => {
        toast.style.opacity = '0'
        setTimeout(() => {
          document.body.removeChild(toast)
        }, 300)
      }, 1500)
    }

    // 清空输入
    const clearInput = () => {
      inputText.value = ''
    }

    // 清空输出
    const clearOutput = () => {
      outputText.value = ''
      rawOutputText.value = ''
      isJsonFormatted.value = false
    }

    // 自定义下拉框方法
    const toggleDropdown = () => {
      isDropdownOpen.value = !isDropdownOpen.value
    }

    const selectAlgorithm = (value) => {
      selectedAlgorithm.value = value
      isDropdownOpen.value = false
    }

    const getSelectedAlgorithmLabel = () => {
      const selected = algorithms.find(algo => algo.value === selectedAlgorithm.value)
      return selected ? selected.label : '请选择算法'
    }

    // 点击外部关闭下拉框
    const handleClickOutside = (event) => {
      if (selectRef.value && !selectRef.value.contains(event.target)) {
        isDropdownOpen.value = false
      }
    }

    // 键盘快捷键处理
    const handleKeydown = (event) => {
      // Ctrl+Enter 或 Cmd+Enter 执行转换
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault()
        processText()
      }
      // // Ctrl+C 复制（当焦点在输出框时）
      // else if ((event.ctrlKey || event.metaKey) && event.key === 'c' && event.target.tagName === 'TEXTAREA' && event.target.readOnly) {
      //   copyResult()
      // }
      // Esc 清空
      else if (event.key === 'Escape') {
        clearInput()
        clearOutput()
      }
    }

    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
      document.addEventListener('click', handleClickOutside)
    })

    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
      document.removeEventListener('click', handleClickOutside)
    })

    return {
      inputText,
      outputText,
      isManualMode,
      isEncryptMode,
      selectedAlgorithm,
      isJsonFormatted,
      showJsonToggle,
      algorithms,
      processText,
      toggleJsonFormat,
      pasteText,
      copyResult,
      clearInput,
      clearOutput,
      isDropdownOpen,
      selectRef,
      toggleDropdown,
      selectAlgorithm,
      getSelectedAlgorithmLabel
    }
  }
}
</script>
