<template>
  <div class="history-page">
    <div class="history-page-header">
      <h2>历史记录({{ history.length }}条)</h2>
      <div class="button-group">
        <button class="btn btn-secondary" @click="clearAllHistory">
          清空全部
        </button>
        <button class="btn btn-primary" @click="$emit('back-to-main')">
          返回主界面
        </button>
      </div>
    </div>

    <div
      v-if="history.length === 0"
      style="text-align: center; color: #666; margin-top: 50px"
    >
      暂无历史记录
    </div>

    <div v-for="item in history" :key="item.id" class="history-item">
      <div class="history-meta">
        <span>{{ formatTime(item.timestamp) }}</span>
        <span style="margin-left: 20px">{{
          getAlgorithmLabel(item.algorithm)
        }}</span>
        <span style="margin-left: 20px">{{
          item.operation === "encrypt" ? "加密" : "解密"
        }}</span>
        <button
          class="btn btn-secondary btn-small"
          style="float: right"
          @click="deleteHistoryItem(item.id)"
        >
          删除
        </button>
        <button
          class="btn btn-secondary btn-small"
          style="float: right; margin-right: 8px"
          @click="useHistoryItem(item)"
        >
          使用
        </button>
      </div>

      <div>
        <div style="font-weight: 500; margin-bottom: 4px">输入:</div>
        <div class="history-content">
          <div
            class="text-ellipsis"
            style="--line-clamp: 4; width: 100%; height: 100%"
            :title="item.input"
          >
            {{ item.input }}
          </div>
        </div>
      </div>

      <div>
        <div style="font-weight: 500; margin-bottom: 4px">输出:</div>
        <div class="history-content">
          <div
            class="text-ellipsis"
            style="--line-clamp: 4; width: 100%; height: 100%"
            :title="item.output"
          >
            {{ item.output }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";
import { storage } from "../utils/storage.js";
import { algorithms } from "../utils/crypto.js";

export default {
  name: "HistoryView",
  emits: ["back-to-main", "use-history-item"],
  setup(props, { emit }) {
    const history = ref([]);

    // 加载历史记录
    const loadHistory = async () => {
      try {
        history.value = await storage.getHistory();
      } catch (error) {
        console.error("加载历史记录失败:", error);
      }
    };

    // 格式化时间
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString("zh-CN");
    };

    // 获取算法标签
    const getAlgorithmLabel = (value) => {
      const algo = algorithms.find((a) => a.value === value);
      return algo ? algo.label : value;
    };

    // 删除历史记录项
    const deleteHistoryItem = async (id) => {
      try {
        await storage.deleteHistoryItem(id);
        await loadHistory();
      } catch (error) {
        console.error("删除历史记录失败:", error);
      }
    };

    // 清空所有历史记录
    const clearAllHistory = async () => {
      if (confirm("确定要清空所有历史记录吗？")) {
        try {
          await storage.clearHistory();
          history.value = [];
        } catch (error) {
          console.error("清空历史记录失败:", error);
        }
      }
    };

    // 使用历史记录项
    const useHistoryItem = (item) => {
      emit("use-history-item", item);
    };

    onMounted(() => {
      loadHistory();
    });

    return {
      history,
      formatTime,
      getAlgorithmLabel,
      deleteHistoryItem,
      clearAllHistory,
      useHistoryItem,
    };
  },
};
</script>
