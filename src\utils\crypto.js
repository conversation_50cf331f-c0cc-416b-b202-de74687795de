import CryptoJS from 'crypto-js'

// 确保CryptoJS正确导入
if (!CryptoJS || !CryptoJS.enc) {
  console.error('CryptoJS导入失败')
  throw new Error('CryptoJS未正确导入')
}

// 验证必要的CryptoJS组件
const requiredComponents = ['AES', 'DES', 'enc', 'mode', 'pad']
for (const component of requiredComponents) {
  if (!CryptoJS[component]) {
    console.error(`CryptoJS.${component} 不可用`)
    throw new Error(`CryptoJS.${component} 组件缺失`)
  }
}

// AES密钥和IV
const key = CryptoJS.enc.Utf8.parse('{MasterCom}16800')
const iv = CryptoJS.enc.Utf8.parse('4567812345678123')

// DES密钥和IV
const keyDES = CryptoJS.enc.Utf8.parse('<PERSON>   ')
const ivDES = CryptoJS.enc.Utf8.parse('12345678')

// AES解密方法
const decryptAES = function (word) {
    let base64string = CryptoJS.enc.Base64.parse(word.replace(/[\r\n]/g, ''))
    let base64Word = base64string.toString()
    let encryptedHexStr = CryptoJS.enc.Hex.parse(base64Word)
    let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
    let decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
    return decryptedStr.toString()
}

// AES加密
const encryptAES = function (word) {
    let srcs = CryptoJS.enc.Utf8.parse(word)
    let encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
    return encrypted.toString()
}

// DES加密 
const encryptDES = function (word) {
    let srcs = CryptoJS.enc.Utf8.parse(word)
    let encrypted = CryptoJS.DES.encrypt(srcs, keyDES, { iv: ivDES, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
    return encrypted.toString()
}

// DES解密
const decryptDES = function (word) {
    let base64string = CryptoJS.enc.Base64.parse(word.replace(/[\r\n]/g, ''))
    let base64Word = base64string.toString()
    let encryptedHexStr = CryptoJS.enc.Hex.parse(base64Word)
    let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
    let decrypt = CryptoJS.DES.decrypt(srcs, keyDES, {
        iv: ivDES,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    })
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
    return decryptedStr.toString()
}

export class MTB64Coder {
    static encode(input) {
        try {
            if (!input || typeof input !== 'string') {
                throw new Error('输入必须是非空字符串')
            }
            var str = CryptoJS.enc.Utf8.parse(input)
            return CryptoJS.enc.Base64.stringify(str)
        } catch (error) {
            console.error('Base64编码失败:', error)
            throw new Error(`Base64编码失败: ${error.message}`)
        }
    }

    static decode(input) {
        try {
            if (!input || typeof input !== 'string') {
                throw new Error('输入必须是非空字符串')
            }
            // 验证Base64格式
            if (!/^[A-Za-z0-9+/]*={0,2}$/.test(input)) {
                throw new Error('无效的Base64格式')
            }
            var words = CryptoJS.enc.Base64.parse(input)
            return words.toString(CryptoJS.enc.Utf8)
        } catch (error) {
            console.error('Base64解码失败:', error)
            throw new Error(`Base64解码失败: ${error.message}`)
        }
    }

    static encodeM2(input) {
        var output = MTB64Coder.encode(input)
        if (output.length > 3) {
            var partPos = Math.floor(output.length / 3)
            var part1 = output.substr(0, partPos)
            var part2 = output.substr(partPos)
            return 'M' + part1 + 'A' + part2 + 'D'
        }
        return output
    }

    static decodeM2(input) {
        var encodeStr
        if (input.length > 6 && input.charAt(0) == 'M') {
            var pos = Math.floor(input.length / 3) - 1
            var part1 = input.substr(1, pos)
            var part2 = input.slice(pos + 2, -1)
            encodeStr = part1 + part2
        } else {
            encodeStr = input
        }
        return MTB64Coder.decode(encodeStr)
    }

    static decodeAes(input) {
        try {
            if (!input || typeof input !== 'string') {
                throw new Error('输入必须是非空字符串')
            }
            return decryptAES(input)
        } catch (error) {
            console.error('AES解密失败:', error)
            throw new Error(`AES解密失败: ${error.message}`)
        }
    }

    static encodeAes(input) {
        try {
            if (!input || typeof input !== 'string') {
                throw new Error('输入必须是非空字符串')
            }
            return encryptAES(input)
        } catch (error) {
            console.error('AES加密失败:', error)
            throw new Error(`AES加密失败: ${error.message}`)
        }
    }

    static encodeDes(input) {
        try {
            if (!input || typeof input !== 'string') {
                throw new Error('输入必须是非空字符串')
            }
            return encryptDES(input)
        } catch (error) {
            console.error('DES加密失败:', error)
            throw new Error(`DES加密失败: ${error.message}`)
        }
    }

    static decodeDes(input) {
        try {
            if (!input || typeof input !== 'string') {
                throw new Error('输入必须是非空字符串')
            }
            return decryptDES(input)
        } catch (error) {
            console.error('DES解密失败:', error)
            throw new Error(`DES解密失败: ${error.message}`)
        }
    }
}

// 加密算法映射
export const encryptFunctions = {
    'aes': MTB64Coder.encodeAes,
    'des': MTB64Coder.encodeDes,
    'base64': MTB64Coder.encode,
    'm2': MTB64Coder.encodeM2
}

// 解密算法映射
export const decryptFunctions = {
    'aes': MTB64Coder.decodeAes,
    'des': MTB64Coder.decodeDes,
    'base64': MTB64Coder.decode,
    'm2': MTB64Coder.decodeM2
}

// 算法列表
export const algorithms = [
    { value: 'aes', label: 'AES', title: 'AES(app后台)' },
    { value: 'des', label: 'DES', title: 'DES(网优之家)' },
    { value: 'base64', label: 'Base64', title: 'Base64编/解码' },
    { value: 'm2', label: 'M2', title: 'M2(密码加密)' }
]

// 处理ENDCODE后缀
export const removeEndCode = (text) => {
    return text.replace(/ENDCODE$/, '')
}

// 检查是否为有效JSON
export const isValidJSON = (str) => {
    try {
        JSON.parse(str)
        return true
    } catch (e) {
        return false
    }
}

// 格式化JSON
export const formatJSON = (str) => {
    try {
        return JSON.stringify(JSON.parse(str), null, 2)
    } catch (e) {
        return str
    }
}
