<template>
  <div id="main">
    <MainView
      v-if="currentView === 'main'"
      @show-history="showHistory"
      :history-data="historyData"
    />
    <HistoryView
      v-else
      @back-to-main="backToMain"
      @use-history-item="useHistoryItem"
    />
  </div>
</template>

<script>
import { ref } from 'vue'
import MainView from './components/MainView.vue'
import HistoryView from './components/HistoryView.vue'

export default {
  name: 'App',
  components: {
    MainView,
    HistoryView
  },
  setup() {
    const currentView = ref('main')
    const historyData = ref(null)

    const showHistory = () => {
      currentView.value = 'history'
    }

    const backToMain = () => {
      currentView.value = 'main'
    }

    const useHistoryItem = (item) => {
      historyData.value = item
      currentView.value = 'main'
    }

    return {
      currentView,
      historyData,
      showHistory,
      backToMain,
      useHistoryItem
    }
  }
}
</script>
