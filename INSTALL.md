# 安装指南

## 🚀 快速安装

### 1. 构建扩展
```bash
# 安装依赖
npm install

# 构建扩展
npm run build
```

### 2. 验证构建
```bash
# 运行验证脚本
node verify-build.js
```

如果看到 "🎉 构建验证通过！" 消息，说明构建成功。

### 3. 安装到Chrome
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择项目中的 `dist` 文件夹
6. 点击"选择文件夹"

### 4. 使用扩展
- 安装成功后，在Chrome工具栏会出现扩展图标
- 点击图标即可打开加密解密工具
- 首次使用可能需要授权剪贴板权限

## 🔧 故障排除

### 问题1：构建失败
**错误信息**：`Cannot read properties of undefined (reading 'enc')`

**解决方案**：
```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 重新构建
npm run build
```

### 问题2：扩展无法加载
**可能原因**：
- manifest.json格式错误
- 文件路径不正确
- 权限配置问题

**解决方案**：
1. 检查dist文件夹是否包含所有必要文件
2. 运行验证脚本：`node verify-build.js`
3. 查看Chrome扩展页面的错误信息

### 问题3：功能不正常
**可能原因**：
- CryptoJS未正确加载
- 算法实现错误

**解决方案**：
1. 打开Chrome开发者工具（F12）
2. 右键点击扩展图标 → "检查弹出内容"
3. 查看控制台错误信息
4. 重新构建扩展

### 问题4：剪贴板功能不工作
**解决方案**：
1. 点击Chrome地址栏左侧的锁图标
2. 允许"剪贴板"权限
3. 刷新扩展页面

## 📁 文件结构说明

构建后的 `dist` 文件夹应包含：
```
dist/
├── manifest.json      # 扩展配置文件
├── index.html         # 弹窗页面
├── main.js           # 主要逻辑代码
├── assets/           # 样式文件
│   └── popup-*.css
└── icons/            # 扩展图标
    ├── icon16.png
    ├── icon32.png
    ├── icon48.png
    └── icon128.png
```

## 🔄 更新扩展

当代码有更新时：
1. 重新构建：`npm run build`
2. 在Chrome扩展页面点击"重新加载"按钮
3. 或者移除扩展后重新安装

## 🎯 功能测试

安装完成后，可以测试以下功能：

### 基本功能测试
1. **Base64编码**：输入"Hello World"，选择Base64，点击加密
2. **Base64解码**：使用上一步的结果，切换到解密模式
3. **AES加密**：输入任意文本，选择AES算法进行加密解密
4. **历史记录**：执行几次操作后，点击历史记录按钮查看

### 高级功能测试
1. **自动模式**：切换到自动模式，输入文本应自动处理
2. **JSON格式化**：解密一个JSON格式的结果，应显示格式化按钮
3. **快捷键**：使用Ctrl+Enter执行转换，Esc清空内容
4. **ENDCODE处理**：在密文末尾添加"ENDCODE"，解密时应自动移除

## 📞 技术支持

如果遇到问题：
1. 查看README.md中的详细说明
2. 检查浏览器控制台的错误信息
3. 确认Chrome版本支持Manifest V3（Chrome 88+）
4. 验证所有依赖是否正确安装

## 🔒 安全说明

- 所有加密解密操作都在本地执行
- 不会向外部服务器发送任何数据
- 历史记录仅保存在本地浏览器中
- 可以随时清空历史记录或卸载扩展
