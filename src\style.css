* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  background-color: #f5f5f5;
}


#main {
  width: 750px;
  height: 600px;
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
}

.container {
  display: flex;
  flex: 1;
  padding: 0;
  gap: 14px;
  min-height: 0;
  width: 100%;
  height: 100%;
}

.input-section,
.output-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  min-height: 0;
  min-width: 0;
}

.control-section {
  width: 100px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  min-height: 0;
  flex-shrink: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.textarea {
  flex: 1;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 14px;
  resize: none;
  outline: none;
  font-family: "Consolas", "Monaco", monospace;
  line-height: 1.6;
  min-height: 0;
  height: 500px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.textarea:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
}

.btn {
  padding: 4px 8px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #545b62 0%, #3d4449 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.btn-small {
  padding: 4px 6px;
  font-size: 12px;
  font-weight: 500;
}

/* 自定义下拉框样式 */
.custom-select {
  position: relative;
  width: 100%;
}

.select-display {
  padding: 4px 8px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  min-height: 20px;
}

.select-display:hover {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.select-arrow {
  font-size: 10px;
  transition: transform 0.2s ease;
  color: #666;
}

.select-arrow.open {
  transform: rotate(180deg);
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
  padding: 0;
  list-style: none;
}

.select-dropdown li {
  padding: 6px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.select-dropdown li:last-child {
  border-bottom: none;
}

.select-dropdown li:hover {
  background-color: #f8f9fa;
}

.select-dropdown li.selected {
  background-color: #007bff;
  color: white;
}

.select-dropdown li.selected:hover {
  background-color: #0056b3;
}

.toggle-btn {
  display: flex;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
}

.toggle-btn button {
  flex: 1;
  padding: 4px 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.toggle-btn button.active {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  transform: scale(1.02);
}

.toggle-btn button:hover:not(.active) {
  background: #e9ecef;
  color: #495057;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
  margin-bottom: 4px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: nowrap;
}

.history-page {
  padding: 0;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 60px;
}

.history-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fff;
  padding-right: 8px;
  height: 60px;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
}

.history-item {
  border: 1px solid #dedede;
  border-radius: 8px;
  padding: 8px;
  background: #fff;
}

.history-item:hover {
  border: 1px solid #7daff9;
}

.history-meta {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.history-content {
  font-family: "Consolas", "Monaco", monospace;
  font-size: 12px;
  background: #efefef;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #eee;
  margin: 4px 0;
  word-break: break-all;
}

.json-toggle {
  margin-top: 8px;
}

.json-formatted {
  white-space: pre-wrap;
  font-family: "Consolas", "Monaco", monospace;
}

/* 响应式设计已移除 - 使用固定布局 */

/* 美化滚动条样式 */
.textarea::-webkit-scrollbar {
  width: 6px;
}

.textarea::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.textarea::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d1d9e6 0%, #b8c5d6 100%);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.textarea::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #b8c5d6 0%, #9fb0c7 100%);
  transform: scale(1.1);
}

.textarea::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, #9fb0c7 0%, #8699b8 100%);
}

/* 通用滚动条样式 */
.history-page::-webkit-scrollbar,
.container::-webkit-scrollbar,
.select-dropdown::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.history-page::-webkit-scrollbar-track,
.container::-webkit-scrollbar-track,
.select-dropdown::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
}

.history-page::-webkit-scrollbar-thumb,
.container::-webkit-scrollbar-thumb,
.select-dropdown::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d1d9e6 0%, #b8c5d6 100%);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.history-page::-webkit-scrollbar-thumb:hover,
.container::-webkit-scrollbar-thumb:hover,
.select-dropdown::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #b8c5d6 0%, #9fb0c7 100%);
}

/* 动画效果 */
.btn {
  transition: all 0.2s ease;
}

.btn:active {
  transform: translateY(1px);
}

.toggle-btn button {
  transition: all 0.2s ease;
}

.control-section {
  transition: all 0.3s ease;
}

/* 提示信息 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 0;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 文本溢出 */
.text-ellipsis {
  --line-clamp: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: var(--line-clamp);
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
