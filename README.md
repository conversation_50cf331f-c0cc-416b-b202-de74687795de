# mt加密解密工具 Chrome 扩展

基于Vue.js开发的Chrome扩展。

## 功能特性

### 🔐 支持的加密算法
- **AES** - app后台加密算法
- **DES** - 网优之家加密算法  
- **Base64** - 标准Base64编解码
- **M2** - 自定义密码加密算法

### 🎯 核心功能
- **双模式操作**：手动模式和自动模式
- **智能识别**：自动识别并移除ENDCODE后缀
- **JSON格式化**：解密结果为JSON时自动提供格式化选项
- **历史记录**：保存所有加解密操作记录
- **快捷操作**：一键粘贴、复制、清空

### 🎨 界面设计
- **左右分栏布局**：输入区域 | 控制区域 | 输出区域
- **响应式设计**：支持不同窗口大小自适应
- **简约风格**：清爽的UI设计，操作直观

### ⌨️ 快捷键支持
- `Ctrl+Enter` / `Cmd+Enter`：执行加密/解密
- `Ctrl+C`：复制输出结果（焦点在输出框时）
- `Esc`：清空输入和输出

## 安装方法

### 1. 构建扩展
```bash
# 安装依赖
npm install

# 构建扩展
npm run build
```

### 2. 加载到Chrome
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目中的 `dist` 文件夹

### 3. 使用扩展
- 点击浏览器工具栏中的扩展图标
- 在弹出的窗口中进行加密解密操作

## 技术架构

### 前端框架
- **Vue 3** - 响应式UI框架
- **Vite** - 现代化构建工具
- **Chrome Extension Manifest V3** - 最新扩展规范

### 存储
- **Chrome Storage API** - 本地存储历史记录

## 项目结构
```
├── public/
│   ├── manifest.json      # 扩展配置文件
│   └── icons/            # 扩展图标
├── src/
│   ├── components/       # Vue组件
│   │   ├── MainView.vue  # 主界面组件
│   │   └── HistoryView.vue # 历史记录组件
│   ├── utils/           # 工具函数
│   │   ├── crypto.js    # 加密解密工具
│   │   ├── crypto-js.js # CryptoJS库
│   │   └── storage.js   # 存储工具
│   ├── App.vue          # 根组件
│   ├── main.js          # 入口文件
│   └── style.css        # 样式文件
├── dist/                # 构建输出目录
├── package.json         # 项目配置
└── vite.config.js       # 构建配置
```